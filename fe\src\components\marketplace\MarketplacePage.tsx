"use client";

import React, { useState, useCallback } from "react";
import { FilterProvider } from "@/contexts/FilterProvider";
import { MarketplaceFilters } from "./MarketplaceFilters";
import { ExpertsGrid } from "./ExpertsGrid";
import { useFilters } from "@/hooks/useFilters";
import { useFilteredExperts } from "@/hooks/useFilteredExperts";
import { ErrorBoundary } from "@/components/common/ErrorBoundary";
import { FilterErrorFallback } from "./FilterErrorFallback";
import { cn } from "@/lib/utils";

interface MarketplacePageProps {
  className?: string;
  onChatClick?: (expertId: number) => void;
}

// Inner component that uses the filter context
const MarketplaceContent: React.FC<MarketplacePageProps> = ({
  className = "",
  onChatClick
}) => {
  const { state } = useFilters();
  const [currentPage, setCurrentPage] = useState(1);
  const [allExperts, setAllExperts] = useState<any[]>([]);

  // Fetch filtered experts
  const { data, loading, error, retryCount, isRetrying, refetch, retry, clearError } = useFilteredExperts({
    filter: state.selectedFilter,
    timeline: state.selectedTimeline,
    search: state.searchQuery,
    page: currentPage,
    limit: 20
  });

  // Handle filter changes - reset to first page and clear accumulated experts
  const handleFiltersChange = useCallback(() => {
    setCurrentPage(1);
    setAllExperts([]);
  }, []);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (data && data.pagination.page < data.pagination.totalPages) {
      setCurrentPage(prev => prev + 1);
    }
  }, [data]);

  // Accumulate experts when loading more pages
  React.useEffect(() => {
    if (data) {
      if (currentPage === 1) {
        // First page or filter change - replace all experts
        setAllExperts(data.experts);
      } else {
        // Additional pages - append to existing experts
        setAllExperts(prev => [...prev, ...data.experts]);
      }
    }
  }, [data, currentPage]);

  const hasMore = data ? data.pagination.page < data.pagination.totalPages : false;
  const loadingMore = loading && currentPage > 1;

  return (
    <div className={cn("container mx-auto px-4 py-8 space-y-8", className)}>
      {/* Filters Section */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
        <MarketplaceFilters onFiltersChange={handleFiltersChange} />
      </div>

      {/* Results Section */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
        {error ? (
          <FilterErrorFallback
            error={new Error(error)}
            onRetry={retry}
            showTrendingFallback={state.selectedFilter !== 'trending'}
          />
        ) : (
          <ExpertsGrid
            experts={allExperts}
            loading={loading && currentPage === 1}
            error={null}
            onChatClick={onChatClick}
            onLoadMore={handleLoadMore}
            hasMore={hasMore}
            loadingMore={loadingMore}
            searchTerm={state.searchQuery}
            emptyStateMessage={
              state.searchQuery
                ? `No experts found for "${state.searchQuery}". Try adjusting your search or filters.`
                : "No experts found matching your current filters. Try adjusting your criteria."
            }
          />
        )}
      </div>
    </div>
  );
};

// Main component with provider and error boundary
export const MarketplacePage: React.FC<MarketplacePageProps> = (props) => {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Marketplace error:', error, errorInfo);
        // Here you could send error to monitoring service
      }}
    >
      <FilterProvider>
        <ErrorBoundary
          fallback={
            <FilterErrorFallback
              error={new Error('Filter system error')}
              showTrendingFallback={true}
            />
          }
        >
          <MarketplaceContent {...props} />
        </ErrorBoundary>
      </FilterProvider>
    </ErrorBoundary>
  );
};