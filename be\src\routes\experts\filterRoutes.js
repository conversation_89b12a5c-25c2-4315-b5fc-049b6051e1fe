const express = require('express');
const expertFilterService = require('../../services/expertFilterService');
const { authenticateToken } = require('../../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Expert Filters
 *   description: Marketplace filtering and search for AI experts
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     FilteredExpertsResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         data:
 *           type: object
 *           properties:
 *             experts:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Expert'
 *             pagination:
 *               type: object
 *               properties:
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 total:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 *             appliedFilters:
 *               type: object
 *               properties:
 *                 filter:
 *                   type: string
 *                 timeline:
 *                   type: string
 *                 search:
 *                   type: string
 */

/**
 * @swagger
 * /api/experts/filtered:
 *   get:
 *     summary: Get filtered experts for marketplace
 *     tags: [Expert Filters]
 *     parameters:
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *           enum: [trending, most-popular, top-rated, recommended, newest]
 *           default: recommended
 *         description: Filter type
 *       - in: query
 *         name: timeline
 *         schema:
 *           type: string
 *           enum: [last-7-days, last-30-days, all-time]
 *           default: last-30-days
 *         description: Timeline filter (for most-popular and top-rated)
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search query
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Results per page
 *     responses:
 *       200:
 *         description: Filtered experts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/FilteredExpertsResponse'
 *       400:
 *         description: Invalid filter parameters
 *       500:
 *         description: Server error
 */
router.get('/filtered', async (req, res) => {
  try {
    const {
      filter = 'recommended',
      timeline = 'last-30-days',
      search = '',
      page = 1,
      limit = 20
    } = req.query;

    // Get user ID from token if available (for personalized recommendations)
    let userId = null;
    if (req.headers.authorization) {
      try {
        const token = req.headers.authorization.split(' ')[1];
        const jwt = require('jsonwebtoken');
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        userId = decoded.userId;
      } catch (error) {
        // Token invalid or expired, continue without user ID
        console.log('Invalid token for personalized recommendations');
      }
    }

    // Validate parameters
    const validFilters = ['trending', 'most-popular', 'top-rated', 'recommended', 'newest'];
    if (!validFilters.includes(filter)) {
      return res.status(400).json({
        success: false,
        error: `Invalid filter type. Must be one of: ${validFilters.join(', ')}`
      });
    }

    const validTimelines = ['last-7-days', 'last-30-days', 'all-time'];
    if (timeline && !validTimelines.includes(timeline)) {
      return res.status(400).json({
        success: false,
        error: `Invalid timeline. Must be one of: ${validTimelines.join(', ')}`
      });
    }

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);

    if (pageNum < 1 || limitNum < 1 || limitNum > 100) {
      return res.status(400).json({
        success: false,
        error: 'Invalid pagination parameters. Page must be >= 1, limit must be 1-100'
      });
    }

    // Get filtered experts
    const result = await expertFilterService.getFilteredExperts({
      filter,
      timeline,
      search,
      page: pageNum,
      limit: limitNum,
      userId
    });

    // Track filter usage for analytics
    await expertFilterService.trackFilterUsage({
      userId,
      filterType: filter,
      timelineFilter: timeline,
      searchQuery: search || null,
      resultsCount: result.experts.length,
      sessionId: req.sessionID,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Error in filtered experts endpoint:', error);

    // Determine appropriate error response based on error type
    let statusCode = 500;
    let errorMessage = 'Failed to retrieve filtered experts';

    if (error.message.includes('Invalid filter type')) {
      statusCode = 400;
      errorMessage = error.message;
    } else if (error.message.includes('Invalid timeline')) {
      statusCode = 400;
      errorMessage = error.message;
    } else if (error.message.includes('Database connection')) {
      statusCode = 503;
      errorMessage = 'Service temporarily unavailable. Please try again in a moment.';
    } else if (error.message.includes('Redis')) {
      // Redis errors shouldn't break the main functionality
      console.warn('Redis cache error, continuing without cache:', error);
      // Don't return error, let the request continue without cache
    } else if (error.message.includes('timeout')) {
      statusCode = 504;
      errorMessage = 'Request timeout. Please try again.';
    }

    // Log error details for monitoring
    console.error('Filter endpoint error details:', {
      message: error.message,
      stack: error.stack,
      query: req.query,
      userId: req.userId || 'anonymous',
      timestamp: new Date().toISOString()
    });

    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      ...(process.env.NODE_ENV === 'development' && {
        details: error.message,
        stack: error.stack
      })
    });
  }
});

/**
 * @swagger
 * /api/experts/filter-analytics:
 *   get:
 *     summary: Get filter usage analytics (Admin only)
 *     tags: [Expert Filters]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 30
 *         description: Number of days to analyze
 *     responses:
 *       200:
 *         description: Filter analytics retrieved successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/filter-analytics', authenticateToken, async (req, res) => {
  try {
    // Check if user is admin (you may need to adjust this based on your auth system)
    if (!req.user || !req.user.isAdmin) {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const { days = 30 } = req.query;
    const daysNum = parseInt(days);

    if (daysNum < 1 || daysNum > 365) {
      return res.status(400).json({
        success: false,
        error: 'Days must be between 1 and 365'
      });
    }

    const { pool } = require('../../config/database');

    // Get filter usage statistics
    const analyticsQuery = `
      SELECT
        filter_type,
        timeline_filter,
        COUNT(*) as usage_count,
        AVG(results_count) as avg_results,
        COUNT(DISTINCT user_id) as unique_users,
        COUNT(DISTINCT session_id) as unique_sessions
      FROM filter_analytics
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      GROUP BY filter_type, timeline_filter
      ORDER BY usage_count DESC
    `;

    const [analytics] = await pool.execute(analyticsQuery, [daysNum]);

    // Get search query statistics
    const searchQuery = `
      SELECT
        search_query,
        COUNT(*) as usage_count,
        AVG(results_count) as avg_results
      FROM filter_analytics
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND search_query IS NOT NULL
        AND search_query != ''
      GROUP BY search_query
      ORDER BY usage_count DESC
      LIMIT 20
    `;

    const [searchStats] = await pool.execute(searchQuery, [daysNum]);

    // Get daily usage trends
    const trendsQuery = `
      SELECT
        DATE(created_at) as date,
        COUNT(*) as total_usage,
        COUNT(DISTINCT user_id) as unique_users
      FROM filter_analytics
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;

    const [trends] = await pool.execute(trendsQuery, [daysNum]);

    res.json({
      success: true,
      data: {
        filterUsage: analytics,
        searchQueries: searchStats,
        dailyTrends: trends,
        period: `${daysNum} days`
      }
    });

  } catch (error) {
    console.error('Error in filter analytics endpoint:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve filter analytics'
    });
  }
});

module.exports = router;