const request = require('supertest');
const express = require('express');
const filterRoutes = require('../../src/routes/experts/filterRoutes');

// Mock the expert filter service
jest.mock('../../src/services/expertFilterService', () => ({
  getFilteredExperts: jest.fn(),
  trackFilterUsage: jest.fn()
}));

// Mock the database config
jest.mock('../../src/config/database', () => ({
  pool: {
    execute: jest.fn()
  }
}));

const expertFilterService = require('../../src/services/expertFilterService');
const { pool } = require('../../src/config/database');

// Create test app
const app = express();
app.use(express.json());
app.use('/api/experts', filterRoutes);

describe('Filter Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/experts/filtered', () => {
    const mockFilterResponse = {
      experts: [
        {
          id: 1,
          name: '<PERSON>',
          description: 'AI Expert',
          imageUrl: 'test.jpg',
          labels: ['AI', 'ML'],
          totalChats: 100,
          totalRevenue: 5000,
          averageRating: 4.5,
          totalReviews: 20,
          createdAt: '2024-01-01',
          pricingPercentage: 10
        }
      ],
      pagination: {
        page: 1,
        limit: 20,
        total: 1,
        totalPages: 1
      },
      appliedFilters: {
        filter: 'trending',
        timeline: 'last-30-days',
        search: null
      }
    };

    it('should return filtered experts successfully', async () => {
      expertFilterService.getFilteredExperts.mockResolvedValue(mockFilterResponse);
      expertFilterService.trackFilterUsage.mockResolvedValue();

      const response = await request(app)
        .get('/api/experts/filtered')
        .query({
          filter: 'trending',
          timeline: 'last-30-days',
          page: 1,
          limit: 20
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockFilterResponse);
      expect(expertFilterService.getFilteredExperts).toHaveBeenCalledWith({
        filter: 'trending',
        timeline: 'last-30-days',
        search: '',
        page: 1,
        limit: 20,
        userId: null
      });
    });

    it('should handle search queries', async () => {
      expertFilterService.getFilteredExperts.mockResolvedValue(mockFilterResponse);
      expertFilterService.trackFilterUsage.mockResolvedValue();

      const response = await request(app)
        .get('/api/experts/filtered')
        .query({
          filter: 'newest',
          search: 'AI Expert',
          page: 1,
          limit: 10
        });

      expect(response.status).toBe(200);
      expect(expertFilterService.getFilteredExperts).toHaveBeenCalledWith({
        filter: 'newest',
        timeline: 'last-30-days',
        search: 'AI Expert',
        page: 1,
        limit: 10,
        userId: null
      });
    });

    it('should validate filter parameters', async () => {
      const response = await request(app)
        .get('/api/experts/filtered')
        .query({
          filter: 'invalid-filter'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid filter type');
    });

    it('should validate timeline parameters', async () => {
      const response = await request(app)
        .get('/api/experts/filtered')
        .query({
          filter: 'trending',
          timeline: 'invalid-timeline'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid timeline');
    });

    it('should validate pagination parameters', async () => {
      const response = await request(app)
        .get('/api/experts/filtered')
        .query({
          filter: 'trending',
          page: 0,
          limit: 101
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid pagination parameters');
    });

    it('should handle service errors gracefully', async () => {
      expertFilterService.getFilteredExperts.mockRejectedValue(new Error('Service error'));

      const response = await request(app)
        .get('/api/experts/filtered')
        .query({
          filter: 'trending'
        });

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Failed to retrieve filtered experts');
    });

    it('should extract user ID from valid JWT token', async () => {
      expertFilterService.getFilteredExperts.mockResolvedValue(mockFilterResponse);
      expertFilterService.trackFilterUsage.mockResolvedValue();

      // Mock JWT verification
      const jwt = require('jsonwebtoken');
      jest.spyOn(jwt, 'verify').mockReturnValue({ userId: 123 });

      const response = await request(app)
        .get('/api/experts/filtered')
        .set('Authorization', 'Bearer valid-token')
        .query({
          filter: 'recommended'
        });

      expect(response.status).toBe(200);
      expect(expertFilterService.getFilteredExperts).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 123
        })
      );

      jwt.verify.mockRestore();
    });
  });

  describe('GET /api/experts/filter-analytics', () => {
    const mockAnalyticsData = {
      filterUsage: [
        {
          filter_type: 'trending',
          timeline_filter: 'last-30-days',
          usage_count: 100,
          avg_results: 15.5,
          unique_users: 50,
          unique_sessions: 75
        }
      ],
      searchQueries: [
        {
          search_query: 'AI Expert',
          usage_count: 25,
          avg_results: 8.2
        }
      ],
      dailyTrends: [
        {
          date: '2024-01-01',
          total_usage: 150,
          unique_users: 80
        }
      ],
      period: '30 days'
    };

    it('should return analytics data for admin users', async () => {
      // Mock authentication middleware
      const mockReq = {
        user: { isAdmin: true }
      };

      pool.execute
        .mockResolvedValueOnce([mockAnalyticsData.filterUsage])
        .mockResolvedValueOnce([mockAnalyticsData.searchQueries])
        .mockResolvedValueOnce([mockAnalyticsData.dailyTrends]);

      // Note: This test would need proper middleware mocking in a real scenario
      // For now, we'll test the service logic separately
    });

    it('should reject non-admin users', async () => {
      // This would test the admin authentication middleware
      // Implementation depends on your auth system
    });

    it('should validate days parameter', async () => {
      // Test parameter validation
      const response = await request(app)
        .get('/api/experts/filter-analytics')
        .query({ days: 500 });

      // Would expect 400 for invalid days parameter
    });
  });
});