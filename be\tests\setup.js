// Test setup file for Jest
require('dotenv').config({ path: '.env.test' });

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.DB_NAME = 'aitrainerhub_test';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';

// Global test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
};

// Global test helpers
global.testHelpers = {
  createMockExpert: (overrides = {}) => ({
    id: 1,
    name: 'Test Expert',
    description: 'Test Description',
    image_url: 'test.jpg',
    labels: '["AI", "ML"]',
    total_chats: 100,
    total_revenue: 5000,
    average_rating: 4.5,
    total_reviews: 20,
    created_at: '2024-01-01',
    pricing_percentage: 10,
    is_public: 1,
    ...overrides
  }),

  createMockFilterResponse: (experts = [], overrides = {}) => ({
    experts,
    pagination: {
      page: 1,
      limit: 20,
      total: experts.length,
      totalPages: Math.ceil(experts.length / 20)
    },
    appliedFilters: {
      filter: 'trending',
      timeline: 'last-30-days',
      search: null
    },
    ...overrides
  })
};

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});