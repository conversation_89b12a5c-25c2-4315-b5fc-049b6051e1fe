{"timestamp":"2025-08-15 06:21:10","level":"debug","message":"New database connection established as id 437","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:10","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:10","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:10","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:10","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:10","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:10","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:10","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:10","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:10","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:10","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:10","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:10","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:10","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:10","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:10","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:11","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:11","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:11","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:11","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:12","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:12","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:12","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:12","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:12","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:12","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:13","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:13","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:14","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:14","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:14","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:14","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:15","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:15","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:15","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:15","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:15","level":"debug","message":"New database connection established as id 438","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:15","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:15","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:16","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:16","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:16","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:16","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:16","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:16","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:16","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:16","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:16","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:16","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:16","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:16","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:16","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:16","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:16","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:16","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:16","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:17","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:17","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:17","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:17","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:17","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:17","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:17","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:17","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:17","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:17","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:18","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:18","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:18","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:18","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:18","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:18","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:18","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:18","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:19","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:19","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:19","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:19","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:19","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:19","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:19","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:19","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:20","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:20","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:20","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:20","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:20","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:20","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:20","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:20","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:21","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:21","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:21","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:21","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:21","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:21","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:21","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:21","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:22","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:22","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:22","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:22","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:22","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:22","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:22","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:22","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:23","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:23","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:23","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:23","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:23","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:23","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:23","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:23","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:24","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:24","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:24","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:24","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:24","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:24","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:25","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:25","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:25","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:25","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:25","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:25","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:25","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:25","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:26","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:26","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:26","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:26","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:26","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:26","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:26","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:26","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:27","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:27","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:27","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:27","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:27","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:27","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:27","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:27","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:28","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:28","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:28","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:28","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:28","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:28","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:28","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:28","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:29","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:29","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:29","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:29","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:29","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:29","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:29","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:29","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:30","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:30","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:30","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:30","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:30","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:30","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:30","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:30","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:31","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:31","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:31","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:31","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:31","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:31","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:31","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:31","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:32","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:32","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:32","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:32","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:32","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:32","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:32","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:32","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:33","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:33","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:33","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:33","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:33","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:33","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:34","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:34","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:34","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:34","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:34","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:34","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:34","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:34","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:35","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:35","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:35","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:35","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:35","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:35","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:35","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:35","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:36","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:36","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:36","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:36","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:36","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:36","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:36","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:36","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:37","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:37","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:37","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:37","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:37","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:37","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:37","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:37","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:38","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:38","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:38","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:38","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:38","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:38","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:38","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:38","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:39","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:39","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:39","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:39","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:39","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:39","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:39","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:39","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:40","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:40","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:40","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:40","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:40","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:40","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:40","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:40","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:41","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:41","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:41","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:41","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:41","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:41","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:41","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:41","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:42","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:42","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:42","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:42","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:42","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:42","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:42","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:42","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:43","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:43","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:43","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:43","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:43","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:43","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:44","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:44","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:44","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:44","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:44","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:44","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:44","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:44","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:45","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:45","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:45","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:45","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:45","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:45","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:45","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:45","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:46","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:46","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:46","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:46","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:46","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:46","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:46","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:46","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:47","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:47","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:47","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:47","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:47","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:47","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:47","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:47","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:48","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:48","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:48","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:48","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:48","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:48","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:48","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:48","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:49","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:49","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:49","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:49","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:49","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:49","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:49","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:49","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:50","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:50","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:50","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:50","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:50","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:50","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:50","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:50","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:51","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:51","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:51","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:51","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:51","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:51","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:52","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:52","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:52","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:52","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:52","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:52","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:53","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:53","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:53","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:53","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:53","level":"debug","message":"New database connection established as id 439","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:53","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:53","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:53","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:53","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:53","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:54","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:54","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:54","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:54","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:54","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:54","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:54","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:54","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:54","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:54","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:54","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:54","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:54","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:54","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:55","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:55","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:55","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:55","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:55","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:55","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:55","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:55","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:55","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:55","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:56","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:56","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:56","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:56","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:56","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:56","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:56","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:56","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:57","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:57","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:57","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:57","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:57","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:57","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:57","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:57","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:58","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:58","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:58","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:58","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:58","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:58","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:58","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:58","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:59","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:59","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:21:59","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:21:59","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:00","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:00","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:00","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:00","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:01","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:01","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:01","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:01","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:02","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:02","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:03","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:03","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:03","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:03","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:04","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:04","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:04","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:04","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:04","level":"debug","message":"New database connection established as id 440","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:04","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:04","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:04","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:04","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:04","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:05","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:05","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:05","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:05","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:05","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:05","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:05","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:05","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:05","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:05","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:05","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:05","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:05","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:05","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:06","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:06","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:06","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:06","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:06","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:06","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:06","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:06","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:06","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:06","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:07","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:07","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:07","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:07","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:07","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:07","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:07","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:07","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:08","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:08","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:08","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:08","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:08","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:08","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:08","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:08","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:09","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:09","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:09","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:09","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:09","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:09","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:09","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:09","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:10","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:10","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:10","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:10","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:10","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:10","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:10","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:10","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:11","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:11","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:11","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:11","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:11","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:11","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:11","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:11","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:12","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:12","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:12","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:12","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:12","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:12","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:12","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:12","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:13","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:13","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:13","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:13","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:13","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:13","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:13","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:13","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:14","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:14","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:14","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:14","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:14","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:14","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:15","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:15","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:15","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:15","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:15","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:15","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:15","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:15","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:16","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:16","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:16","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:16","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:16","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:16","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:16","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:16","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:17","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:17","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:17","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:17","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:17","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:17","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:17","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:17","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:18","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:18","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:18","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:18","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:18","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:18","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:18","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:18","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:19","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:19","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:19","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:19","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:19","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:19","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:19","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:19","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:20","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:20","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:20","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:20","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:20","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:20","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:21","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:21","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:21","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:21","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:21","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:21","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:21","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:21","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:22","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:22","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:22","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:22","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:22","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:22","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:22","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:22","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:23","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:23","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:23","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:23","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:23","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:23","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:23","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:23","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:24","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:24","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:24","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:24","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:24","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:24","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:24","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:24","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:25","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:25","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:25","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:25","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:25","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:25","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:25","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:25","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:26","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:26","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:26","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:26","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:26","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:26","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:26","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:26","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:27","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:27","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:27","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:27","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:27","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:27","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:27","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:27","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:28","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:28","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:28","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:28","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:28","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:28","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:28","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:28","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:29","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:29","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:29","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:29","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:29","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:29","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:29","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:29","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:30","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:30","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:30","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:30","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:30","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:30","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:30","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:30","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:31","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:31","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:31","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:31","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:31","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:31","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:31","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:31","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:32","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:32","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:32","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:32","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:32","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:32","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:32","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:32","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:33","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:33","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:33","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:33","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:33","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:33","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:33","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:33","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:34","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:34","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:34","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:34","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:34","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:34","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:34","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:34","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:35","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:35","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:35","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:35","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:35","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:35","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:35","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:35","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:36","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:36","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:36","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:36","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:36","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:36","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:37","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:37","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:37","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:37","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:37","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:37","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:37","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:37","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:38","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:38","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:38","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:38","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:38","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:38","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:38","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:38","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:39","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:39","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:39","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:39","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:39","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:39","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:39","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:39","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:40","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:40","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:40","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:40","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:40","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:40","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:41","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:41","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:41","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:41","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:41","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:41","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:41","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:41","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:42","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:42","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:42","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:42","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:42","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:42","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:42","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:42","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:43","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:43","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:43","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:43","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:43","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:43","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:43","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:43","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:44","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:44","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:44","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:44","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:44","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:44","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:44","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:44","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:45","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:45","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:45","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:45","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:45","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:45","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:45","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:45","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:46","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:46","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:46","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:46","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:46","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:46","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:46","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:46","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:47","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:47","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:47","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:47","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:47","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:47","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:47","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:47","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:48","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:48","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:48","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:48","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:48","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:48","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:48","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:48","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:49","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:49","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:49","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:49","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:49","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:49","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:49","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:49","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:50","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:50","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:50","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:50","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:50","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:50","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:50","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:50","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:51","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:51","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:51","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:51","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:51","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:51","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:51","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:51","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:52","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:52","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:52","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:52","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:52","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:52","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:52","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:52","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:53","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:53","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:53","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:53","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:53","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:53","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:53","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:53","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:54","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:54","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:54","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:54","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:54","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:54","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:54","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:54","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:55","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:55","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:55","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:55","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:55","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:55","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:55","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:55","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:56","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:56","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:56","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:56","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:56","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:56","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:56","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:56","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:57","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:57","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:57","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:57","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:57","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:57","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:57","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:58","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:58","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:58","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:58","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:58","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:58","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:58","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:59","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:59","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:59","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:59","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:59","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:59","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:22:59","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:22:59","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:00","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:00","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:00","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:00","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:00","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:00","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:00","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:00","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:01","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:01","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:01","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:01","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:01","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:01","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:01","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:01","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:02","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:02","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:02","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:02","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:02","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:02","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:03","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:03","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:03","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:03","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:03","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:03","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:03","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:03","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:04","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:04","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:04","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:04","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:04","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:04","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:04","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:04","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:05","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:05","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:05","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:05","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:05","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:05","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:05","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:05","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:06","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:28","level":"debug","message":"New database connection established as id 441","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:28","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:28","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:28","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:28","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:28","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:39","level":"info","message":"SIGINT received, shutting down gracefully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:42","level":"debug","message":"New database connection established as id 442","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:42","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:42","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:42","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:42","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:42","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:42","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:42","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:42","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:42","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:42","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:42","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:42","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:23:47","level":"error","message":"Unhandled error in request","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"req.get is not a function","stack":"TypeError: req.get is not a function\n    at logger.apiRequest (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\utils\\logger.js:133:24)\n    at requestLogger (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\errorHandler.js:185:12)\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:328:13)","name":"TypeError"},"context":{"method":"GET","path":"/","query":{"ide_webview_request_time":"1755213827797"},"body":{},"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36"}}
{"timestamp":"2025-08-15 06:25:49","level":"debug","message":"New database connection established as id 443","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:25:49","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:25:50","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:25:50","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:25:50","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:25:50","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:25:50","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:25:50","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:25:50","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:25:50","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:25:50","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:25:50","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:25:50","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:24","level":"debug","message":"New database connection established as id 444","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:24","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:24","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:24","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:24","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:24","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:24","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:24","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:24","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:24","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:24","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:24","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:24","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:37","level":"debug","message":"New database connection established as id 445","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:37","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:37","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:37","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:37","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:37","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:37","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:37","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:37","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:37","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:37","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:37","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:26:37","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:23","level":"debug","message":"New database connection established as id 446","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:23","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:23","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:23","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:23","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:23","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:23","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:23","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:23","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:23","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:23","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:23","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:23","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:35","level":"debug","message":"New database connection established as id 447","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:35","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:35","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:35","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:35","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:35","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:35","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:35","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:35","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:35","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:35","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:35","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:27:35","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:29:07","level":"debug","message":"New database connection established as id 448","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:29:07","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:29:07","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:29:07","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:29:07","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:29:07","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:29:07","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:29:07","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:29:07","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:29:07","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:29:07","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:29:07","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:29:07","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:30:00","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:30:00","level":"error","message":"Database query error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":"Unknown column 'id' in 'field list'","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","params":"none"}
{"timestamp":"2025-08-15 06:30:00","level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Unknown column 'id' in 'field list'","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)","name":"Error"},"context":{}}
{"timestamp":"2025-08-15 06:30:00","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 06:30:00","level":"debug","message":"New database connection established as id 449","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:00:00","level":"error","message":"Failed to clean expired cache: cacheManager.cleanExpiredCache is not a function","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","stack":"TypeError: cacheManager.cleanExpiredCache is not a function\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:159:32)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)"}
{"timestamp":"2025-08-15 07:00:00","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:00:00","level":"error","message":"Database query error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":"Unknown column 'id' in 'field list'","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","params":"none"}
{"timestamp":"2025-08-15 07:00:00","level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Unknown column 'id' in 'field list'","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)","name":"Error"},"context":{}}
{"timestamp":"2025-08-15 07:00:00","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:35:56","level":"debug","message":"New database connection established as id 468","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:35:56","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:35:56","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:35:56","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:35:56","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:35:56","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:35:56","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:35:56","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:35:56","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:35:56","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:35:56","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:35:56","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:35:56","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:07","level":"debug","message":"New database connection established as id 469","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:07","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:07","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:07","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:07","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:07","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:07","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:07","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:07","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:07","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:07","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:07","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:07","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:26","level":"error","message":"Unhandled error in request","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"req.get is not a function","stack":"TypeError: req.get is not a function\n    at logger.apiRequest (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\utils\\logger.js:133:24)\n    at requestLogger (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\errorHandler.js:185:12)\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:328:13)","name":"TypeError"},"context":{"method":"GET","path":"/health","query":{},"body":{},"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157"}}
{"timestamp":"2025-08-15 07:36:43","level":"debug","message":"New database connection established as id 470","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:43","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:43","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:43","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:43","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:43","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:43","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:43","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:43","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:43","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:43","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:43","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:43","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:55","level":"debug","message":"New database connection established as id 471","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:55","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:55","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:55","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:55","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:55","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:55","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:55","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:55","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:55","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:55","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:55","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:36:55","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:01","level":"error","message":"Unhandled error in request","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Cannot read properties of undefined (reading 'user-agent')","stack":"TypeError: Cannot read properties of undefined (reading 'user-agent')\n    at logger.apiRequest (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\utils\\logger.js:133:31)\n    at requestLogger (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\errorHandler.js:185:12)\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:328:13)","name":"TypeError"},"context":{"method":"GET","path":"/health","query":{},"body":{},"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157"}}
{"timestamp":"2025-08-15 07:37:19","level":"debug","message":"New database connection established as id 472","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:19","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:19","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:19","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:19","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:19","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:19","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:19","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:19","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:19","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:19","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:19","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:19","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:31","level":"debug","message":"New database connection established as id 473","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:31","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:31","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:31","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:31","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:31","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:31","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:31","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:31","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:31","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:31","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:31","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:31","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:37:37","level":"error","message":"Unhandled error in request","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"logger.businessMetrics is not a function","stack":"TypeError: logger.businessMetrics is not a function\n    at res.send (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\errorHandler.js:265:16)\n    at ServerResponse.json (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\response.js:278:15)\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:107:21\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:280:10)","name":"TypeError"},"context":{"method":"GET","path":"/health","query":{},"body":{},"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157"}}
{"timestamp":"2025-08-15 07:38:48","level":"debug","message":"New database connection established as id 474","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:38:48","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:38:48","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:38:48","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:38:48","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:38:48","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:38:48","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:38:48","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:38:48","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:38:48","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:38:48","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:38:48","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:38:48","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:00","level":"debug","message":"New database connection established as id 475","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:00","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:00","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:00","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:00","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:00","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:00","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:00","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:00","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:00","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:00","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:00","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:00","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:06","level":"info","message":"API Request Completed","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","method":"GET","path":"/","statusCode":404,"responseTime":1}
{"timestamp":"2025-08-15 07:39:06","level":"error","message":"API Error","duration":"3ms","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","method":"GET","url":"/health","statusCode":404,"userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157","ip":"::1","userId":"anonymous"}
{"timestamp":"2025-08-15 07:39:17","level":"info","message":"API Request Completed","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","method":"GET","path":"/:userId","statusCode":401,"responseTime":1}
{"timestamp":"2025-08-15 07:39:17","level":"error","message":"API Error","duration":"2ms","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","method":"GET","url":"/api/recommendations/health","statusCode":401,"userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157","ip":"::1","userId":"anonymous"}
{"timestamp":"2025-08-15 07:39:27","level":"info","message":"Valid API key used","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","serviceName":"test-service","keyPrefix":"your-sec..."}
{"timestamp":"2025-08-15 07:39:27","level":"error","message":"Service authentication failed","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Cannot read properties of undefined (reading 'user-agent')","stack":"TypeError: Cannot read properties of undefined (reading 'user-agent')\n    at logger.apiRequest (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\utils\\logger.js:133:31)\n    at authenticateService (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\auth.js:37:16)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"TypeError"},"context":{"ip":"::1","serviceName":"test-service","path":"/health"}}
{"timestamp":"2025-08-15 07:39:27","level":"info","message":"API Request Completed","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","method":"GET","path":"/:userId","statusCode":500,"responseTime":4}
{"timestamp":"2025-08-15 07:39:27","level":"error","message":"API Error","duration":"5ms","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","method":"GET","url":"/api/recommendations/health","statusCode":500,"userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157","ip":"::1","userId":"anonymous"}
{"timestamp":"2025-08-15 07:39:46","level":"debug","message":"New database connection established as id 476","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:46","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:46","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:46","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:46","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:46","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:46","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:46","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:46","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:46","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:46","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:46","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:46","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:52","level":"debug","message":"New database connection established as id 477","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:52","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:52","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:52","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:52","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:52","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:52","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:52","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:52","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:52","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:52","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:52","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:39:52","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:04","level":"debug","message":"New database connection established as id 478","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:04","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:04","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:04","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:04","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:04","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:04","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:04","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:04","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:04","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:04","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:04","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:04","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:10","level":"info","message":"Valid API key used","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","serviceName":"test-service","keyPrefix":"your-sec..."}
{"timestamp":"2025-08-15 07:40:10","level":"error","message":"Unhandled error in request","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Invalid user ID","stack":"ValidationError: Invalid user ID\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\routes\\recommendations.js:67:19\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\errorHandler.js:97:25\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateService (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\auth.js:38:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"ValidationError"},"context":{"method":"GET","path":"/api/recommendations/health","query":{},"body":{},"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157"}}
{"timestamp":"2025-08-15 07:40:10","level":"info","message":"API Request Completed","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","method":"GET","path":"/:userId","statusCode":400,"responseTime":6}
{"timestamp":"2025-08-15 07:40:10","level":"error","message":"API Error","duration":"7ms","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","method":"GET","url":"/api/recommendations/health","statusCode":400,"userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157","ip":"::1","userId":"anonymous"}
{"timestamp":"2025-08-15 07:40:58","level":"debug","message":"New database connection established as id 479","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:58","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:58","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:58","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:58","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:58","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:58","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:58","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:58","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:58","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:58","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:58","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:40:58","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:04","level":"debug","message":"New database connection established as id 480","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:04","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:04","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:04","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:04","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:04","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:04","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:04","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:04","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:04","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:04","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:04","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:04","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:17","level":"debug","message":"New database connection established as id 481","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:17","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:17","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:17","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:17","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:17","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:17","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:17","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:17","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:17","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:17","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:17","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:17","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:41:24","level":"info","message":"Valid API key used","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","serviceName":"test-service","keyPrefix":"your-sec..."}
{"timestamp":"2025-08-15 07:41:24","level":"error","message":"Unhandled error in request","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Invalid user ID","stack":"ValidationError: Invalid user ID\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\routes\\recommendations.js:67:19\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\errorHandler.js:97:25\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateService (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\auth.js:38:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"ValidationError"},"context":{"method":"GET","path":"/api/recommendations/health","query":{},"body":{},"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157"}}
{"timestamp":"2025-08-15 07:41:24","level":"info","message":"API Request Completed","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","method":"GET","path":"/:userId","statusCode":400,"responseTime":7}
{"timestamp":"2025-08-15 07:41:24","level":"error","message":"API Error","duration":"8ms","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","method":"GET","url":"/api/recommendations/health","statusCode":400,"userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157","ip":"::1","userId":"anonymous"}
{"timestamp":"2025-08-15 07:42:16","level":"debug","message":"New database connection established as id 482","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:42:16","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:42:16","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:42:16","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:42:16","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:42:16","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:42:16","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:42:16","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:42:16","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:42:16","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:42:16","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:42:16","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:42:16","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:43:48","level":"debug","message":"New database connection established as id 483","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:43:48","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:43:48","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:43:48","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:43:48","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:43:48","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:43:48","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:43:48","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:43:48","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:43:48","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:43:48","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:43:48","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:43:48","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:02","level":"debug","message":"New database connection established as id 484","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:02","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:02","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:02","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:02","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:02","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:02","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:02","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:02","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:02","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:02","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:02","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:02","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:14","level":"debug","message":"New database connection established as id 485","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:14","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:14","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:14","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:14","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:14","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:14","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:14","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:14","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:14","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:14","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:14","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:14","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 07:44:21","level":"info","message":"API Request Completed","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","method":"GET","path":"/health","statusCode":401,"responseTime":2}
{"timestamp":"2025-08-15 07:44:21","level":"error","message":"API Error","duration":"3ms","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","method":"GET","url":"/api/recommendations/health","statusCode":401,"userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157","ip":"::1","userId":"anonymous"}
{"timestamp":"2025-08-15 07:44:31","level":"info","message":"Valid API key used","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","serviceName":"ai-trainer-main","keyPrefix":"your-sec..."}
{"timestamp":"2025-08-15 07:44:31","level":"error","message":"Cache ping failed","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"redis.ping is not a function","stack":"TypeError: redis.ping is not a function\n    at CacheManager.ping (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\CacheManager.js:373:40)\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\routes\\recommendations.js:70:52\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"TypeError"},"context":{}}
{"timestamp":"2025-08-15 07:44:31","level":"error","message":"Failed to get cache stats","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"redis.info is not a function","stack":"TypeError: redis.info is not a function\n    at CacheManager.getCacheStats (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\CacheManager.js:386:38)\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\routes\\recommendations.js:86:51\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"TypeError"},"context":{}}
{"timestamp":"2025-08-15 07:44:31","level":"info","message":"API Request Completed","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","method":"GET","path":"/health","statusCode":503,"responseTime":75}
{"timestamp":"2025-08-15 07:44:31","level":"error","message":"API Error","duration":"76ms","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","method":"GET","url":"/api/recommendations/health","statusCode":503,"userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157","ip":"::1","userId":"anonymous"}
{"timestamp":"2025-08-15 08:00:00","level":"error","message":"Failed to clean expired cache: cacheManager.cleanExpiredCache is not a function","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","stack":"TypeError: cacheManager.cleanExpiredCache is not a function\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:159:32)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)"}
{"timestamp":"2025-08-15 08:00:00","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:00:00","level":"error","message":"Database query error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":"Unknown column 'id' in 'field list'","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","params":"none"}
{"timestamp":"2025-08-15 08:00:00","level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Unknown column 'id' in 'field list'","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)","name":"Error"},"context":{}}
{"timestamp":"2025-08-15 08:00:00","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:00:00","level":"debug","message":"New database connection established as id 492","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:00","level":"debug","message":"New database connection established as id 493","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:00","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:00","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:00","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:00","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:00","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:00","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:00","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:00","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:00","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:00","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:00","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:00","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:23","level":"debug","message":"New database connection established as id 494","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:23","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:23","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:23","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:23","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:23","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:23","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:23","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:23","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:23","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:23","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:23","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:12:23","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:30:00","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:30:00","level":"error","message":"Database query error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":"Unknown column 'id' in 'field list'","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","params":"none"}
{"timestamp":"2025-08-15 08:30:00","level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Unknown column 'id' in 'field list'","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)","name":"Error"},"context":{}}
{"timestamp":"2025-08-15 08:30:00","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:30:00","level":"debug","message":"New database connection established as id 501","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:39:52","level":"debug","message":"New database connection established as id 504","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:39:52","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:39:52","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:39:52","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:39:52","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:39:52","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:39:52","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:39:52","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:39:52","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:39:52","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:39:52","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:39:52","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 08:39:52","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 09:00:00","level":"error","message":"Failed to clean expired cache: cacheManager.cleanExpiredCache is not a function","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","stack":"TypeError: cacheManager.cleanExpiredCache is not a function\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:159:32)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)"}
{"timestamp":"2025-08-15 09:00:00","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 09:00:00","level":"error","message":"Database query error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":"Unknown column 'id' in 'field list'","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","params":"none"}
{"timestamp":"2025-08-15 09:00:00","level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Unknown column 'id' in 'field list'","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)","name":"Error"},"context":{}}
{"timestamp":"2025-08-15 09:00:00","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 09:30:00","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 09:30:00","level":"error","message":"Database query error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":"Unknown column 'id' in 'field list'","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","params":"none"}
{"timestamp":"2025-08-15 09:30:00","level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Unknown column 'id' in 'field list'","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)","name":"Error"},"context":{}}
{"timestamp":"2025-08-15 09:30:00","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 10:00:00","level":"error","message":"Failed to clean expired cache: cacheManager.cleanExpiredCache is not a function","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","stack":"TypeError: cacheManager.cleanExpiredCache is not a function\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:159:32)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)"}
{"timestamp":"2025-08-15 10:00:00","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 10:00:00","level":"error","message":"Database query error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":"Unknown column 'id' in 'field list'","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","params":"none"}
{"timestamp":"2025-08-15 10:00:00","level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Unknown column 'id' in 'field list'","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)","name":"Error"},"context":{}}
{"timestamp":"2025-08-15 10:00:00","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 10:30:00","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 10:30:00","level":"error","message":"Database query error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":"Unknown column 'id' in 'field list'","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","params":"none"}
{"timestamp":"2025-08-15 10:30:00","level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Unknown column 'id' in 'field list'","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)","name":"Error"},"context":{}}
{"timestamp":"2025-08-15 10:30:00","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 11:00:00","level":"error","message":"Failed to clean expired cache: cacheManager.cleanExpiredCache is not a function","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","stack":"TypeError: cacheManager.cleanExpiredCache is not a function\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:159:32)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)"}
{"timestamp":"2025-08-15 11:00:00","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 11:00:00","level":"debug","message":"New database connection established as id 529","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 11:00:00","level":"error","message":"Database query error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":"Unknown column 'id' in 'field list'","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","params":"none"}
{"timestamp":"2025-08-15 11:00:00","level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Unknown column 'id' in 'field list'","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)","name":"Error"},"context":{}}
{"timestamp":"2025-08-15 11:00:00","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 11:30:00","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 11:30:00","level":"error","message":"Database query error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":"Unknown column 'id' in 'field list'","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","params":"none"}
{"timestamp":"2025-08-15 11:30:00","level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Unknown column 'id' in 'field list'","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)","name":"Error"},"context":{}}
{"timestamp":"2025-08-15 11:30:00","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 12:00:00","level":"error","message":"Failed to clean expired cache: cacheManager.cleanExpiredCache is not a function","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","stack":"TypeError: cacheManager.cleanExpiredCache is not a function\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:159:32)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)"}
{"timestamp":"2025-08-15 12:00:00","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 12:00:00","level":"info","message":"Starting user similarity recalculation","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 12:00:00","level":"error","message":"Database query error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":"Unknown column 'id' in 'field list'","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","params":"none"}
{"timestamp":"2025-08-15 12:00:00","level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Unknown column 'id' in 'field list'","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)","name":"Error"},"context":{}}
{"timestamp":"2025-08-15 12:00:00","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 12:00:00","level":"debug","message":"New database connection established as id 530","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 12:00:00","level":"info","message":"Not enough users for similarity calculation","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 12:00:00","level":"info","message":"User similarities recalculated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 12:30:00","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 12:30:00","level":"error","message":"Database query error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":"Unknown column 'id' in 'field list'","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","params":"none"}
{"timestamp":"2025-08-15 12:30:00","level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Unknown column 'id' in 'field list'","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)","name":"Error"},"context":{}}
{"timestamp":"2025-08-15 12:30:00","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:00:00","level":"error","message":"Failed to clean expired cache: cacheManager.cleanExpiredCache is not a function","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","stack":"TypeError: cacheManager.cleanExpiredCache is not a function\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:159:32)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)"}
{"timestamp":"2025-08-15 13:00:00","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:00:00","level":"error","message":"Database query error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":"Unknown column 'id' in 'field list'","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","params":"none"}
{"timestamp":"2025-08-15 13:00:00","level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Unknown column 'id' in 'field list'","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)","name":"Error"},"context":{}}
{"timestamp":"2025-08-15 13:00:00","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:30:00","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:30:00","level":"error","message":"Database query error:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":"Unknown column 'id' in 'field list'","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","params":"none"}
{"timestamp":"2025-08-15 13:30:00","level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","error":{"message":"Unknown column 'id' in 'field list'","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)","name":"Error"},"context":{}}
{"timestamp":"2025-08-15 13:30:00","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:41:49","level":"debug","message":"New database connection established as id 537","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:41:49","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:41:49","level":"info","message":"Database connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:41:49","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:41:49","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:41:49","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:41:49","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:41:49","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:41:49","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:41:49","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:41:49","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:41:49","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-15 13:41:49","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-16 16:09:15","level":"error","message":"Database connection test failed:","service":"ai-trainer-recommender","version":"1.0.0","environment":"development","code":"ECONNREFUSED","fatal":true,"stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)"}
