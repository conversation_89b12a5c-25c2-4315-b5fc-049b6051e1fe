# Marketplace Filtering System - Deployment Guide

## Overview

This document provides comprehensive deployment instructions for the AI Trainer Hub Marketplace Filtering System. The system includes advanced filtering, search, caching, and analytics capabilities.

## System Architecture

### Frontend Components
- **FilterProvider**: Context provider for filter state management
- **MarketplaceFilters**: Main filter interface with search and filter controls
- **ExpertsGrid**: Responsive grid displaying filtered experts
- **ExpertCard**: Individual expert display with search highlighting
- **ErrorBoundary**: Error handling and fallback components

### Backend Services
- **ExpertFilterService**: Core filtering logic with caching
- **Redis Caching**: Performance optimization with TTL-based caching
- **Analytics Tracking**: Filter usage analytics and monitoring
- **Error Handling**: Comprehensive error handling and logging

## Prerequisites

### System Requirements
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- 4GB+ RAM
- 10GB+ disk space

### Environment Variables

#### Backend (.env)
```env
# Database Configuration
DB_HOST=localhost
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=aitrainerhub
DB_CONNECTION_LIMIT=20
DB_QUEUE_LIMIT=0
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000
DB_IDLE_TIMEOUT=600000

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT Configuration
JWT_SECRET=your_jwt_secret

# Environment
NODE_ENV=production
```

#### Frontend (.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_ENVIRONMENT=production
```

## Database Setup

### 1. Run Database Migrations

```sql
-- Add required columns to experts table
ALTER TABLE experts
ADD COLUMN total_chats INT DEFAULT 0,
ADD COLUMN total_revenue DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN average_rating DECIMAL(3,2) DEFAULT 0.00,
ADD COLUMN total_reviews INT DEFAULT 0,
ADD COLUMN is_public BOOLEAN DEFAULT TRUE,
ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Create indexes for optimal performance
CREATE INDEX idx_experts_filter_trending ON experts (total_chats, average_rating, created_at);
CREATE INDEX idx_experts_filter_popular ON experts (total_chats, total_revenue);
CREATE INDEX idx_experts_filter_rating ON experts (average_rating, total_reviews);
CREATE INDEX idx_experts_filter_newest ON experts (created_at);
CREATE INDEX idx_experts_search ON experts (name, description);
CREATE INDEX idx_experts_public ON experts (is_public);

-- Create analytics table
CREATE TABLE filter_analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    filter_type VARCHAR(50) NOT NULL,
    timeline_filter VARCHAR(50) NULL,
    search_query TEXT NULL,
    results_count INT DEFAULT 0,
    session_id VARCHAR(255) NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_analytics_filter (filter_type, created_at),
    INDEX idx_analytics_user (user_id, created_at),
    INDEX idx_analytics_session (session_id, created_at)
);

-- Create trending stats view
CREATE VIEW expert_trending_stats AS
SELECT
    e.id,
    e.name,
    e.description,
    e.image_url,
    e.labels,
    e.total_chats,
    e.total_revenue,
    e.average_rating,
    e.total_reviews,
    e.created_at,
    e.pricing_percentage,
    e.is_public,
    -- Trending score calculation
    (
        (e.total_chats * 0.4) +
        (e.average_rating * e.total_reviews * 0.3) +
        (CASE
            WHEN DATEDIFF(NOW(), e.created_at) <= 7 THEN 20
            WHEN DATEDIFF(NOW(), e.created_at) <= 30 THEN 10
            ELSE 0
        END * 0.3)
    ) as trending_score
FROM experts e
WHERE e.is_public = TRUE;
```

### 2. Populate Sample Data (Optional)

```sql
-- Update existing experts with sample data
UPDATE experts SET
    total_chats = FLOOR(RAND() * 1000) + 10,
    total_revenue = ROUND(RAND() * 50000 + 1000, 2),
    average_rating = ROUND(RAND() * 2 + 3, 1),
    total_reviews = FLOOR(RAND() * 200) + 5,
    is_public = TRUE
WHERE id <= 10;
```

## Redis Setup

### 1. Install Redis
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install redis-server

# CentOS/RHEL
sudo yum install redis

# macOS
brew install redis

# Windows
# Download from https://redis.io/download
```

### 2. Configure Redis
```bash
# Edit redis.conf
sudo nano /etc/redis/redis.conf

# Key configurations:
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 3. Start Redis
```bash
sudo systemctl start redis
sudo systemctl enable redis
```

## Backend Deployment

### 1. Install Dependencies
```bash
cd be
npm install
```

### 2. Run Tests
```bash
npm test
```

### 3. Start Application
```bash
# Development
npm run dev

# Production
npm start
```

### 4. Warm Up Cache (Optional)
```bash
node src/scripts/warmCache.js
```

## Frontend Deployment

### 1. Install Dependencies
```bash
cd fe
npm install
```

### 2. Build Application
```bash
npm run build
```

### 3. Start Application
```bash
# Development
npm run dev

# Production
npm start
```

## Integration Steps

### 1. Update Existing Marketplace Page

Replace the existing expert listing component with the new MarketplacePage:

```tsx
// pages/marketplace.tsx or app/marketplace/page.tsx
import { MarketplacePage } from '@/components/marketplace';

export default function Marketplace() {
  const handleChatClick = (expertId: number) => {
    // Your existing chat logic
    router.push(`/chat/${expertId}`);
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">AI Expert Marketplace</h1>
      <MarketplacePage onChatClick={handleChatClick} />
    </div>
  );
}
```

### 2. Update API Routes

Ensure the filter routes are properly mounted in your main Express app:

```javascript
// server.js or app.js
const filterRoutes = require('./src/routes/experts/filterRoutes');
app.use('/api/experts', filterRoutes);

// Add error handling middleware
const errorHandler = require('./src/middleware/errorHandler');
app.use(errorHandler);
```

## Performance Optimization

### 1. Cache Warming Schedule

Set up a cron job to warm the cache periodically:

```bash
# Add to crontab (crontab -e)
*/30 * * * * cd /path/to/be && node src/scripts/warmCache.js
```

### 2. Database Optimization

Monitor and optimize slow queries:

```sql
-- Enable slow query log
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;

-- Monitor performance
SHOW PROCESSLIST;
EXPLAIN SELECT * FROM experts WHERE ...;
```

### 3. Redis Monitoring

```bash
# Monitor Redis performance
redis-cli info memory
redis-cli info stats
redis-cli monitor
```

## Monitoring and Logging

### 1. Application Logs

Monitor application logs for errors:

```bash
# Backend logs
tail -f logs/app.log

# Filter-specific logs
grep "Filter" logs/app.log
```

### 2. Performance Metrics

Key metrics to monitor:
- Filter response times
- Cache hit rates
- Database query performance
- Error rates
- User engagement with filters

### 3. Health Checks

```bash
# Database health
curl http://localhost:3001/api/health/database

# Redis health
redis-cli ping

# Application health
curl http://localhost:3001/api/health
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check database credentials
   - Verify database server is running
   - Check connection limits

2. **Redis Connection Errors**
   - Verify Redis server is running
   - Check Redis configuration
   - Monitor Redis memory usage

3. **Slow Filter Performance**
   - Check database indexes
   - Monitor cache hit rates
   - Optimize database queries

4. **Frontend Build Errors**
   - Clear node_modules and reinstall
   - Check TypeScript errors
   - Verify environment variables

### Performance Tuning

1. **Database Optimization**
   ```sql
   -- Analyze table performance
   ANALYZE TABLE experts;

   -- Check index usage
   SHOW INDEX FROM experts;
   ```

2. **Cache Optimization**
   ```bash
   # Adjust Redis memory settings
   redis-cli config set maxmemory 2gb
   redis-cli config set maxmemory-policy allkeys-lru
   ```

3. **Application Tuning**
   - Adjust connection pool sizes
   - Optimize cache TTL values
   - Monitor memory usage

## Security Considerations

### 1. Input Validation
- All filter parameters are validated
- SQL injection protection enabled
- XSS protection in place

### 2. Rate Limiting
- API endpoints have rate limiting
- Cache prevents abuse
- Monitor for unusual patterns

### 3. Authentication
- JWT tokens for personalized features
- Graceful degradation for anonymous users
- Secure token handling

## Rollback Plan

If issues occur during deployment:

1. **Database Rollback**
   ```sql
   -- Remove added columns if needed
   ALTER TABLE experts
   DROP COLUMN total_chats,
   DROP COLUMN total_revenue,
   DROP COLUMN average_rating,
   DROP COLUMN total_reviews;
   ```

2. **Application Rollback**
   - Revert to previous application version
   - Restore previous marketplace component
   - Clear Redis cache if needed

3. **Frontend Rollback**
   - Deploy previous frontend build
   - Update API endpoints if needed

## Post-Deployment Checklist

- [ ] Database migrations completed successfully
- [ ] Redis server running and accessible
- [ ] Backend application starts without errors
- [ ] Frontend application builds and runs
- [ ] Filter endpoints respond correctly
- [ ] Search functionality works
- [ ] Caching is operational
- [ ] Error handling works as expected
- [ ] Analytics tracking is functional
- [ ] Performance is acceptable
- [ ] Monitoring is in place

## Support and Maintenance

### Regular Maintenance Tasks

1. **Weekly**
   - Monitor cache performance
   - Check error logs
   - Review analytics data

2. **Monthly**
   - Optimize database indexes
   - Clean up old analytics data
   - Update cache warming schedule

3. **Quarterly**
   - Performance review
   - Security audit
   - Capacity planning

### Contact Information

For technical support or questions about this deployment:
- Development Team: [<EMAIL>]
- System Administrator: [<EMAIL>]
- Emergency Contact: [<EMAIL>]

---

**Last Updated**: August 16, 2025
**Version**: 1.0.0
**Author**: AI Development Team
```