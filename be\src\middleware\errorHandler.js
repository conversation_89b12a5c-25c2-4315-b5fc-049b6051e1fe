const errorHandler = (err, req, res, next) => {
  // Log error with context
  console.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  // Default error
  let error = {
    message: err.message || 'Internal Server Error',
    status: err.status || 500,
    code: err.code || 'INTERNAL_ERROR'
  };

  // Database connection errors
  if (err.code === 'ECONNREFUSED' || err.code === 'ENOTFOUND' || err.code === 'ETIMEDOUT') {
    error.message = 'Database connection error. Please try again later.';
    error.status = 503;
    error.code = 'DATABASE_ERROR';
  }

  // MySQL specific errors
  if (err.code === 'ER_ACCESS_DENIED_ERROR') {
    error.message = 'Database access denied';
    error.status = 503;
    error.code = 'DATABASE_ACCESS_ERROR';
  }

  if (err.code === 'ER_BAD_DB_ERROR') {
    error.message = 'Database not found';
    error.status = 503;
    error.code = 'DATABASE_NOT_FOUND';
  }

  // Redis connection errors
  if (err.message && err.message.includes('Redis')) {
    error.message = 'Cache service temporarily unavailable';
    error.status = 503;
    error.code = 'CACHE_ERROR';
  }

  // OpenAI API errors
  if (err.name === 'OpenAIError') {
    error.message = 'AI service temporarily unavailable';
    error.status = 503;
    error.code = 'AI_SERVICE_ERROR';
  }

  // Validation errors
  if (err.name === 'ValidationError') {
    error.message = 'Invalid request data';
    error.status = 400;
    error.code = 'VALIDATION_ERROR';
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    error.message = 'Invalid authentication token';
    error.status = 401;
    error.code = 'INVALID_TOKEN';
  }

  if (err.name === 'TokenExpiredError') {
    error.message = 'Authentication token expired';
    error.status = 401;
    error.code = 'TOKEN_EXPIRED';
  }

  // Rate limiting errors
  if (err.status === 429) {
    error.message = 'Too many requests. Please try again later.';
    error.code = 'RATE_LIMIT_EXCEEDED';
  }

  // Filter-specific errors
  if (err.message && err.message.includes('Invalid filter')) {
    error.status = 400;
    error.code = 'INVALID_FILTER';
  }

  if (err.message && err.message.includes('Invalid timeline')) {
    error.status = 400;
    error.code = 'INVALID_TIMELINE';
  }

  // Don't expose internal errors in production
  if (process.env.NODE_ENV === 'production' && error.status === 500) {
    error.message = 'Internal server error';
  }

  res.status(error.status).json({
    success: false,
    error: error.message,
    code: error.code,
    ...(process.env.NODE_ENV === 'development' && {
      stack: err.stack,
      details: err.message
    })
  });
};

module.exports = errorHandler;