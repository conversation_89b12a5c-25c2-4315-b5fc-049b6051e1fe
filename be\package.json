{"name": "be", "version": "1.0.0", "description": "Backend proxy for AI Trainer Hub", "main": "server.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "logs": "node view-logs.js", "logs:today": "node view-logs.js today", "logs:stats": "node view-logs.js stats", "logs:clean": "node view-logs.js clean", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"axios": "^1.11.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.2", "openai": "^4.67.0", "redis": "^5.8.1", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0"}, "devDependencies": {"jest": "^30.0.5", "nodemon": "^3.1.10", "supertest": "^7.1.4"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/server.js", "!src/config/database.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "testMatch": ["**/tests/**/*.test.js"], "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"]}}