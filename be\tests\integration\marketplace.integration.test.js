/**
 * Integration tests for the complete marketplace filtering system
 * Tests the full flow from API endpoints to database queries
 */

const request = require('supertest');
const express = require('express');
const filterRoutes = require('../../src/routes/experts/filterRoutes');
const { pool } = require('../../src/config/database');
const redisClient = require('../../src/config/redis');

// Create test app
const app = express();
app.use(express.json());
app.use('/api/experts', filterRoutes);

describe('Marketplace Filtering System Integration', () => {
  beforeAll(async () => {
    // Ensure database connection
    await pool.execute('SELECT 1');

    // Clear Redis cache
    try {
      await redisClient.flushPattern('filter:*');
    } catch (error) {
      console.warn('Redis not available for tests');
    }
  });

  afterAll(async () => {
    // Clean up connections
    await pool.end();
    await redisClient.close();
  });

  describe('Complete Filter Flow', () => {
    it('should handle trending filter with timeline', async () => {
      const response = await request(app)
        .get('/api/experts/filtered')
        .query({
          filter: 'trending',
          timeline: 'last-30-days',
          page: 1,
          limit: 10
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('experts');
      expect(response.body.data).toHaveProperty('pagination');
      expect(response.body.data).toHaveProperty('appliedFilters');
      expect(response.body.data.appliedFilters.filter).toBe('trending');
      expect(response.body.data.appliedFilters.timeline).toBe('last-30-days');
    });

    it('should handle search with filters', async () => {
      const response = await request(app)
        .get('/api/experts/filtered')
        .query({
          filter: 'newest',
          search: 'AI',
          page: 1,
          limit: 5
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.appliedFilters.search).toBe('AI');
      expect(response.body.data.pagination.limit).toBe(5);
    });

    it('should handle pagination correctly', async () => {
      const page1Response = await request(app)
        .get('/api/experts/filtered')
        .query({
          filter: 'most-popular',
          page: 1,
          limit: 3
        });

      expect(page1Response.status).toBe(200);
      expect(page1Response.body.data.pagination.page).toBe(1);
      expect(page1Response.body.data.pagination.limit).toBe(3);

      if (page1Response.body.data.pagination.totalPages > 1) {
        const page2Response = await request(app)
          .get('/api/experts/filtered')
          .query({
            filter: 'most-popular',
            page: 2,
            limit: 3
          });

        expect(page2Response.status).toBe(200);
        expect(page2Response.body.data.pagination.page).toBe(2);
      }
    });

    it('should validate filter parameters', async () => {
      const response = await request(app)
        .get('/api/experts/filtered')
        .query({
          filter: 'invalid-filter'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid filter type');
    });

    it('should validate timeline parameters', async () => {
      const response = await request(app)
        .get('/api/experts/filtered')
        .query({
          filter: 'trending',
          timeline: 'invalid-timeline'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid timeline');
    });

    it('should validate pagination parameters', async () => {
      const response = await request(app)
        .get('/api/experts/filtered')
        .query({
          filter: 'trending',
          page: 0,
          limit: 101
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid pagination parameters');
    });
  });

  describe('Performance and Caching', () => {
    it('should cache filter results', async () => {
      const filterParams = {
        filter: 'top-rated',
        timeline: 'last-30-days',
        page: 1,
        limit: 10
      };

      // First request (should hit database)
      const start1 = Date.now();
      const response1 = await request(app)
        .get('/api/experts/filtered')
        .query(filterParams);
      const time1 = Date.now() - start1;

      expect(response1.status).toBe(200);

      // Second request (should hit cache if Redis is available)
      const start2 = Date.now();
      const response2 = await request(app)
        .get('/api/experts/filtered')
        .query(filterParams);
      const time2 = Date.now() - start2;

      expect(response2.status).toBe(200);
      expect(response2.body.data).toEqual(response1.body.data);

      // Cache should be faster (if Redis is available)
      if (process.env.REDIS_HOST) {
        expect(time2).toBeLessThan(time1);
      }
    });

    it('should handle high load', async () => {
      const promises = [];
      const concurrentRequests = 10;

      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          request(app)
            .get('/api/experts/filtered')
            .query({
              filter: 'trending',
              page: Math.floor(i / 3) + 1,
              limit: 5
            })
        );
      }

      const responses = await Promise.all(promises);

      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // This test would require mocking database errors
      // For now, we'll test that the endpoint doesn't crash
      const response = await request(app)
        .get('/api/experts/filtered')
        .query({
          filter: 'trending',
          timeline: 'last-30-days'
        });

      // Should either succeed or fail gracefully
      expect([200, 500, 503]).toContain(response.status);
      expect(response.body).toHaveProperty('success');
    });

    it('should handle malformed requests', async () => {
      const response = await request(app)
        .get('/api/experts/filtered')
        .query({
          filter: 'trending',
          page: 'invalid',
          limit: 'invalid'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });
});