2025-08-16T09:09:38.119Z - {
  "requestId": "s0e7bshou",
  "timestamp": "2025-08-16T09:09:38.118Z",
  "duration": "13ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/public",
    "path": "/public",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 500,
    "headers": {},
    "body": {
      "success": false,
      "error": "Failed to get public experts",
      "message": "connect ECONNREFUSED 127.0.0.1:3306"
    },
    "size": "104 bytes"
  }
}
2025-08-16T09:09:38.124Z - {
  "requestId": "s0e7bshou",
  "timestamp": "2025-08-16T09:09:38.124Z",
  "duration": "19ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/public",
    "path": "/public",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 500,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "success": false,
      "error": "Failed to get public experts",
      "message": "connect ECONNREFUSED 127.0.0.1:3306"
    },
    "size": "104 bytes"
  }
}
