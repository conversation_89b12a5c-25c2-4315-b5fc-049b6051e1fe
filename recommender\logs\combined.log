{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:11","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:11","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:11","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:11","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:12","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:12","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:12","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:12","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:12","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:12","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:13","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:13","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:14","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:14","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:15","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:15","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:15","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:15","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:15","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:15","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:17","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:17","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:17","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:17","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:17","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:18","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:18","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:18","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:18","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:18","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:18","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:18","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:18","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:19","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:19","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:19","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:19","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:20","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:20","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:20","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:20","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:20","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:20","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:20","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:20","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:21","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:21","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:21","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:21","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:21","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:21","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:21","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:21","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:22","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:22","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:22","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:22","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:22","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:22","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:22","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:22","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:23","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:23","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:23","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:23","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:24","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:24","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:24","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:25","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:25","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:25","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:25","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:25","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:25","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:25","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:25","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:26","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:26","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:26","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:26","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:26","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:26","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:26","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:26","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:27","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:27","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:27","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:27","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:27","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:27","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:27","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:27","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:28","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:28","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:28","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:28","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:28","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:28","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:28","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:28","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:29","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:29","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:29","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:29","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:29","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:29","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:29","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:29","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:30","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:30","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:30","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:30","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:30","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:30","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:30","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:30","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:31","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:31","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:31","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:31","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:32","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:32","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:32","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:32","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:32","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:32","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:32","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:32","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:33","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:33","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:33","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:33","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:33","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:33","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:34","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:34","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:34","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:34","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:34","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:34","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:34","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:34","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:35","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:35","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:35","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:35","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:36","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:36","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:36","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:36","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:36","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:36","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:36","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:36","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:37","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:37","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:37","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:37","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:38","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:38","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:38","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:38","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:38","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:38","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:38","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:38","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:39","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:39","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:39","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:39","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:39","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:39","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:39","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:39","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:40","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:40","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:40","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:40","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:40","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:40","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:40","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:40","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:41","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:41","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:41","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:41","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:41","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:41","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:41","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:41","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:42","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:42","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:42","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:42","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:43","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:43","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:43","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:44","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:44","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:44","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:44","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:44","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:44","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:44","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:44","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:45","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:45","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:45","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:45","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:45","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:45","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:45","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:45","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:46","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:46","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:46","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:46","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:47","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:47","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:47","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:47","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:47","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:47","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:47","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:47","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:48","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:48","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:48","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:48","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:49","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:49","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:49","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:49","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:50","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:50","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:50","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:50","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:51","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:51","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:51","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:51","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:51","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:51","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:52","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:52","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:52","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:53","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:53","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:53","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:53","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:53","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:53","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:53","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:53","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:53","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:54","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:54","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:54","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:54","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:54","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:54","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:54","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:54","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:54","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:54","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:54","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:54","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:54","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:54","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:55","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:55","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:55","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:55","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:55","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:56","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:56","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:56","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:56","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:57","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:57","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:57","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:57","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:57","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:57","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:57","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:57","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:58","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:58","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:58","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:58","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:59","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:59","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:21:59","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:21:59","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:00","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:00","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:01","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:01","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:01","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:01","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:02","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:03","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:03","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:03","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:03","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:04","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:04","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:04","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:04","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:05","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:05","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:05","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:05","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:05","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:05","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:05","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:05","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:05","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:05","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:05","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:05","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:05","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:05","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:06","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:06","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:06","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:06","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:06","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:07","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:07","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:07","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:07","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:08","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:08","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:08","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:08","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:08","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:08","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:08","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:08","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:09","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:09","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:09","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:09","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:09","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:09","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:09","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:09","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:10","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:10","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:10","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:10","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:11","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:11","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:11","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:11","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:11","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:11","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:11","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:11","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:12","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:12","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:12","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:12","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:12","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:12","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:12","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:12","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:13","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:13","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:13","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:13","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:13","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:13","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:13","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:13","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:14","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:14","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:14","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:15","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:15","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:15","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:15","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:15","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:15","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:15","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:15","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:16","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:16","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:16","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:16","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:17","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:17","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:17","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:17","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:18","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:18","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:18","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:18","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:18","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:18","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:18","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:18","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:19","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:19","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:19","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:19","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:20","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:20","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:20","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:20","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:20","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:20","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:21","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:21","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:21","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:21","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:21","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:21","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:21","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:21","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:22","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:22","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:22","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:22","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:22","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:22","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:22","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:22","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:23","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:23","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:23","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:23","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:24","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:24","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:24","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:24","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:25","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:25","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:25","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:25","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:25","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:25","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:25","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:25","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:26","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:26","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:26","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:26","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:26","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:26","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:26","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:26","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:27","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:27","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:27","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:27","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:27","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:27","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:27","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:27","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:28","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:28","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:28","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:28","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:28","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:28","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:28","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:28","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:29","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:29","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:29","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:29","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:29","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:29","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:29","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:29","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:30","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:30","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:30","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:30","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:30","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:30","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:30","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:30","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:31","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:31","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:31","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:31","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:32","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:32","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:32","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:32","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:32","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:32","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:32","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:32","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:33","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:33","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:33","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:33","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:33","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:33","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:33","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:33","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:34","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:34","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:34","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:34","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:34","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:34","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:34","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:34","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:35","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:35","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:35","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:35","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:36","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:36","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:36","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:36","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:36","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:36","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:37","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:37","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:37","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:37","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:38","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:38","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:38","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:38","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:38","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:38","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:38","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:38","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:39","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:39","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:39","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:39","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:39","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:39","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:39","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:39","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:40","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:40","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:40","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:40","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:40","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:40","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:41","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:41","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:41","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:41","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:41","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:41","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:41","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:41","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:42","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:42","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:42","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:42","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:43","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:43","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:43","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:43","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:44","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:44","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:44","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:44","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:44","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:44","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:44","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:44","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:45","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:45","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:45","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:45","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:45","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:45","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:45","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:45","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:46","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:46","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:46","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:46","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:47","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:47","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:47","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:47","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:47","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:47","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:47","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:47","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:48","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:48","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:48","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:48","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:49","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:49","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:49","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:49","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:50","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:50","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:50","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:50","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:51","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:51","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:51","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:51","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:51","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:51","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:51","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:51","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:52","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:52","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:52","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:52","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:53","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:53","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:53","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:53","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:53","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:53","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:53","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:53","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:54","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:54","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:54","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:54","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:54","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:54","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:54","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:54","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:55","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:55","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:55","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:55","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:56","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:56","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:56","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:56","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:57","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:57","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:57","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:57","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:57","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:57","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:57","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:58","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:58","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:58","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:59","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:59","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:59","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:59","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:59","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:59","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:22:59","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:22:59","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:00","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:00","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:00","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:00","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:01","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:01","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:01","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:01","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:01","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:01","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:01","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:01","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:02","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:02","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:02","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:03","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:03","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:03","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:03","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:03","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:03","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:03","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:03","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:04","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:04","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:04","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:04","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:05","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:05","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:05","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:05","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:05","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:05","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:05","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:05","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","level":"error","message":"Redis client error:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client reconnecting...","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:28","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:28","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:28","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:28","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:28","version":"1.0.0"}
{"environment":"development","level":"info","message":"SIGINT received, shutting down gracefully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:39","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:42","version":"1.0.0"}
{"context":{"body":{},"ip":"::1","method":"GET","path":"/","query":{"ide_webview_request_time":"1755213827797"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36"},"environment":"development","error":{"message":"req.get is not a function","name":"TypeError","stack":"TypeError: req.get is not a function\n    at logger.apiRequest (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\utils\\logger.js:133:24)\n    at requestLogger (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\errorHandler.js:185:12)\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:328:13)"},"level":"error","message":"Unhandled error in request","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:23:47","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:25:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:25:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:25:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:25:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:25:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:25:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:25:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:25:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:25:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:25:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:25:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:25:50","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:26:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:27:35","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:29:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:29:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:29:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:29:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:29:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:29:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:29:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:29:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:29:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:29:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:29:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:29:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:30:00","version":"1.0.0"}
{"environment":"development","error":"Unknown column 'id' in 'field list'","level":"error","message":"Database query error:","params":"none","service":"ai-trainer-recommender","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","timestamp":"2025-08-15 06:30:00","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"Unknown column 'id' in 'field list'","name":"Error","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)"},"level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:30:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","timestamp":"2025-08-15 06:30:00","version":"1.0.0"}
{"environment":"development","level":"error","message":"Failed to clean expired cache: cacheManager.cleanExpiredCache is not a function","service":"ai-trainer-recommender","stack":"TypeError: cacheManager.cleanExpiredCache is not a function\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:159:32)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-08-15 07:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:00:00","version":"1.0.0"}
{"environment":"development","error":"Unknown column 'id' in 'field list'","level":"error","message":"Database query error:","params":"none","service":"ai-trainer-recommender","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","timestamp":"2025-08-15 07:00:00","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"Unknown column 'id' in 'field list'","name":"Error","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)"},"level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:35:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:35:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:35:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:35:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:35:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:35:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:35:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:35:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:35:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:35:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:35:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:35:56","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:07","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:07","version":"1.0.0"}
{"context":{"body":{},"ip":"::1","method":"GET","path":"/health","query":{},"userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157"},"environment":"development","error":{"message":"req.get is not a function","name":"TypeError","stack":"TypeError: req.get is not a function\n    at logger.apiRequest (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\utils\\logger.js:133:24)\n    at requestLogger (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\errorHandler.js:185:12)\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:328:13)"},"level":"error","message":"Unhandled error in request","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:26","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:36:55","version":"1.0.0"}
{"context":{"body":{},"ip":"::1","method":"GET","path":"/health","query":{},"userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157"},"environment":"development","error":{"message":"Cannot read properties of undefined (reading 'user-agent')","name":"TypeError","stack":"TypeError: Cannot read properties of undefined (reading 'user-agent')\n    at logger.apiRequest (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\utils\\logger.js:133:31)\n    at requestLogger (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\errorHandler.js:185:12)\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:328:13)"},"level":"error","message":"Unhandled error in request","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:01","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:19","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:31","version":"1.0.0"}
{"context":{"body":{},"ip":"::1","method":"GET","path":"/health","query":{},"userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157"},"environment":"development","error":{"message":"logger.businessMetrics is not a function","name":"TypeError","stack":"TypeError: logger.businessMetrics is not a function\n    at res.send (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\errorHandler.js:265:16)\n    at ServerResponse.json (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\response.js:278:15)\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:107:21\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\index.js:280:10)"},"level":"error","message":"Unhandled error in request","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:37:37","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:38:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:38:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:38:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:38:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:38:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:38:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:38:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:38:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:38:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:38:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:38:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:38:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"API Request Completed","method":"GET","path":"/","responseTime":1,"service":"ai-trainer-recommender","statusCode":404,"timestamp":"2025-08-15 07:39:06","version":"1.0.0"}
{"duration":"3ms","environment":"development","ip":"::1","level":"error","message":"API Error","method":"GET","service":"ai-trainer-recommender","statusCode":404,"timestamp":"2025-08-15 07:39:06","url":"/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157","userId":"anonymous","version":"1.0.0"}
{"environment":"development","level":"info","message":"API Request Completed","method":"GET","path":"/:userId","responseTime":1,"service":"ai-trainer-recommender","statusCode":401,"timestamp":"2025-08-15 07:39:17","version":"1.0.0"}
{"duration":"2ms","environment":"development","ip":"::1","level":"error","message":"API Error","method":"GET","service":"ai-trainer-recommender","statusCode":401,"timestamp":"2025-08-15 07:39:17","url":"/api/recommendations/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157","userId":"anonymous","version":"1.0.0"}
{"environment":"development","keyPrefix":"your-sec...","level":"info","message":"Valid API key used","service":"ai-trainer-recommender","serviceName":"test-service","timestamp":"2025-08-15 07:39:27","version":"1.0.0"}
{"context":{"ip":"::1","path":"/health","serviceName":"test-service"},"environment":"development","error":{"message":"Cannot read properties of undefined (reading 'user-agent')","name":"TypeError","stack":"TypeError: Cannot read properties of undefined (reading 'user-agent')\n    at logger.apiRequest (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\utils\\logger.js:133:31)\n    at authenticateService (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\auth.js:37:16)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"},"level":"error","message":"Service authentication failed","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:27","version":"1.0.0"}
{"environment":"development","level":"info","message":"API Request Completed","method":"GET","path":"/:userId","responseTime":4,"service":"ai-trainer-recommender","statusCode":500,"timestamp":"2025-08-15 07:39:27","version":"1.0.0"}
{"duration":"5ms","environment":"development","ip":"::1","level":"error","message":"API Error","method":"GET","service":"ai-trainer-recommender","statusCode":500,"timestamp":"2025-08-15 07:39:27","url":"/api/recommendations/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157","userId":"anonymous","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:46","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:04","version":"1.0.0"}
{"environment":"development","keyPrefix":"your-sec...","level":"info","message":"Valid API key used","service":"ai-trainer-recommender","serviceName":"test-service","timestamp":"2025-08-15 07:40:10","version":"1.0.0"}
{"context":{"body":{},"ip":"::1","method":"GET","path":"/api/recommendations/health","query":{},"userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157"},"environment":"development","error":{"message":"Invalid user ID","name":"ValidationError","stack":"ValidationError: Invalid user ID\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\routes\\recommendations.js:67:19\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\errorHandler.js:97:25\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateService (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\auth.js:38:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"},"level":"error","message":"Unhandled error in request","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"API Request Completed","method":"GET","path":"/:userId","responseTime":6,"service":"ai-trainer-recommender","statusCode":400,"timestamp":"2025-08-15 07:40:10","version":"1.0.0"}
{"duration":"7ms","environment":"development","ip":"::1","level":"error","message":"API Error","method":"GET","service":"ai-trainer-recommender","statusCode":400,"timestamp":"2025-08-15 07:40:10","url":"/api/recommendations/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157","userId":"anonymous","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:40:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:04","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:17","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:17","version":"1.0.0"}
{"environment":"development","keyPrefix":"your-sec...","level":"info","message":"Valid API key used","service":"ai-trainer-recommender","serviceName":"test-service","timestamp":"2025-08-15 07:41:24","version":"1.0.0"}
{"context":{"body":{},"ip":"::1","method":"GET","path":"/api/recommendations/health","query":{},"userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157"},"environment":"development","error":{"message":"Invalid user ID","name":"ValidationError","stack":"ValidationError: Invalid user ID\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\routes\\recommendations.js:67:19\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\errorHandler.js:97:25\n    at Layer.handle [as handle_request] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateService (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\middleware\\auth.js:38:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"},"level":"error","message":"Unhandled error in request","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:41:24","version":"1.0.0"}
{"environment":"development","level":"info","message":"API Request Completed","method":"GET","path":"/:userId","responseTime":7,"service":"ai-trainer-recommender","statusCode":400,"timestamp":"2025-08-15 07:41:24","version":"1.0.0"}
{"duration":"8ms","environment":"development","ip":"::1","level":"error","message":"API Error","method":"GET","service":"ai-trainer-recommender","statusCode":400,"timestamp":"2025-08-15 07:41:24","url":"/api/recommendations/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157","userId":"anonymous","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:42:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:42:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:42:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:42:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:42:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:42:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:42:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:42:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:42:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:42:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:42:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:42:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:43:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:43:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:43:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:43:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:43:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:43:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:43:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:43:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:43:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:43:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:43:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:43:48","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:02","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:14","version":"1.0.0"}
{"environment":"development","level":"info","message":"API Request Completed","method":"GET","path":"/health","responseTime":2,"service":"ai-trainer-recommender","statusCode":401,"timestamp":"2025-08-15 07:44:21","version":"1.0.0"}
{"duration":"3ms","environment":"development","ip":"::1","level":"error","message":"API Error","method":"GET","service":"ai-trainer-recommender","statusCode":401,"timestamp":"2025-08-15 07:44:21","url":"/api/recommendations/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157","userId":"anonymous","version":"1.0.0"}
{"environment":"development","keyPrefix":"your-sec...","level":"info","message":"Valid API key used","service":"ai-trainer-recommender","serviceName":"ai-trainer-main","timestamp":"2025-08-15 07:44:31","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"redis.ping is not a function","name":"TypeError","stack":"TypeError: redis.ping is not a function\n    at CacheManager.ping (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\CacheManager.js:373:40)\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\routes\\recommendations.js:70:52\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"},"level":"error","message":"Cache ping failed","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:31","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"redis.info is not a function","name":"TypeError","stack":"TypeError: redis.info is not a function\n    at CacheManager.getCacheStats (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\CacheManager.js:386:38)\n    at D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\routes\\recommendations.js:86:51\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"},"level":"error","message":"Failed to get cache stats","service":"ai-trainer-recommender","timestamp":"2025-08-15 07:44:31","version":"1.0.0"}
{"environment":"development","level":"info","message":"API Request Completed","method":"GET","path":"/health","responseTime":75,"service":"ai-trainer-recommender","statusCode":503,"timestamp":"2025-08-15 07:44:31","version":"1.0.0"}
{"duration":"76ms","environment":"development","ip":"::1","level":"error","message":"API Error","method":"GET","service":"ai-trainer-recommender","statusCode":503,"timestamp":"2025-08-15 07:44:31","url":"/api/recommendations/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6157","userId":"anonymous","version":"1.0.0"}
{"environment":"development","level":"error","message":"Failed to clean expired cache: cacheManager.cleanExpiredCache is not a function","service":"ai-trainer-recommender","stack":"TypeError: cacheManager.cleanExpiredCache is not a function\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:159:32)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-08-15 08:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:00:00","version":"1.0.0"}
{"environment":"development","error":"Unknown column 'id' in 'field list'","level":"error","message":"Database query error:","params":"none","service":"ai-trainer-recommender","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","timestamp":"2025-08-15 08:00:00","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"Unknown column 'id' in 'field list'","name":"Error","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)"},"level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:12:23","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:30:00","version":"1.0.0"}
{"environment":"development","error":"Unknown column 'id' in 'field list'","level":"error","message":"Database query error:","params":"none","service":"ai-trainer-recommender","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","timestamp":"2025-08-15 08:30:00","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"Unknown column 'id' in 'field list'","name":"Error","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)"},"level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:30:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:30:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:39:52","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 08:39:52","version":"1.0.0"}
{"environment":"development","level":"error","message":"Failed to clean expired cache: cacheManager.cleanExpiredCache is not a function","service":"ai-trainer-recommender","stack":"TypeError: cacheManager.cleanExpiredCache is not a function\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:159:32)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-08-15 09:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","timestamp":"2025-08-15 09:00:00","version":"1.0.0"}
{"environment":"development","error":"Unknown column 'id' in 'field list'","level":"error","message":"Database query error:","params":"none","service":"ai-trainer-recommender","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","timestamp":"2025-08-15 09:00:00","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"Unknown column 'id' in 'field list'","name":"Error","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)"},"level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","timestamp":"2025-08-15 09:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","timestamp":"2025-08-15 09:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","timestamp":"2025-08-15 09:30:00","version":"1.0.0"}
{"environment":"development","error":"Unknown column 'id' in 'field list'","level":"error","message":"Database query error:","params":"none","service":"ai-trainer-recommender","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","timestamp":"2025-08-15 09:30:00","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"Unknown column 'id' in 'field list'","name":"Error","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)"},"level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","timestamp":"2025-08-15 09:30:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","timestamp":"2025-08-15 09:30:00","version":"1.0.0"}
{"environment":"development","level":"error","message":"Failed to clean expired cache: cacheManager.cleanExpiredCache is not a function","service":"ai-trainer-recommender","stack":"TypeError: cacheManager.cleanExpiredCache is not a function\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:159:32)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-08-15 10:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","timestamp":"2025-08-15 10:00:00","version":"1.0.0"}
{"environment":"development","error":"Unknown column 'id' in 'field list'","level":"error","message":"Database query error:","params":"none","service":"ai-trainer-recommender","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","timestamp":"2025-08-15 10:00:00","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"Unknown column 'id' in 'field list'","name":"Error","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)"},"level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","timestamp":"2025-08-15 10:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","timestamp":"2025-08-15 10:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","timestamp":"2025-08-15 10:30:00","version":"1.0.0"}
{"environment":"development","error":"Unknown column 'id' in 'field list'","level":"error","message":"Database query error:","params":"none","service":"ai-trainer-recommender","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","timestamp":"2025-08-15 10:30:00","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"Unknown column 'id' in 'field list'","name":"Error","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)"},"level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","timestamp":"2025-08-15 10:30:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","timestamp":"2025-08-15 10:30:00","version":"1.0.0"}
{"environment":"development","level":"error","message":"Failed to clean expired cache: cacheManager.cleanExpiredCache is not a function","service":"ai-trainer-recommender","stack":"TypeError: cacheManager.cleanExpiredCache is not a function\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:159:32)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-08-15 11:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","timestamp":"2025-08-15 11:00:00","version":"1.0.0"}
{"environment":"development","error":"Unknown column 'id' in 'field list'","level":"error","message":"Database query error:","params":"none","service":"ai-trainer-recommender","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","timestamp":"2025-08-15 11:00:00","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"Unknown column 'id' in 'field list'","name":"Error","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)"},"level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","timestamp":"2025-08-15 11:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","timestamp":"2025-08-15 11:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","timestamp":"2025-08-15 11:30:00","version":"1.0.0"}
{"environment":"development","error":"Unknown column 'id' in 'field list'","level":"error","message":"Database query error:","params":"none","service":"ai-trainer-recommender","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","timestamp":"2025-08-15 11:30:00","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"Unknown column 'id' in 'field list'","name":"Error","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)"},"level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","timestamp":"2025-08-15 11:30:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","timestamp":"2025-08-15 11:30:00","version":"1.0.0"}
{"environment":"development","level":"error","message":"Failed to clean expired cache: cacheManager.cleanExpiredCache is not a function","service":"ai-trainer-recommender","stack":"TypeError: cacheManager.cleanExpiredCache is not a function\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:159:32)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-08-15 12:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","timestamp":"2025-08-15 12:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting user similarity recalculation","service":"ai-trainer-recommender","timestamp":"2025-08-15 12:00:00","version":"1.0.0"}
{"environment":"development","error":"Unknown column 'id' in 'field list'","level":"error","message":"Database query error:","params":"none","service":"ai-trainer-recommender","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","timestamp":"2025-08-15 12:00:00","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"Unknown column 'id' in 'field list'","name":"Error","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)"},"level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","timestamp":"2025-08-15 12:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","timestamp":"2025-08-15 12:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Not enough users for similarity calculation","service":"ai-trainer-recommender","timestamp":"2025-08-15 12:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"User similarities recalculated","service":"ai-trainer-recommender","timestamp":"2025-08-15 12:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","timestamp":"2025-08-15 12:30:00","version":"1.0.0"}
{"environment":"development","error":"Unknown column 'id' in 'field list'","level":"error","message":"Database query error:","params":"none","service":"ai-trainer-recommender","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","timestamp":"2025-08-15 12:30:00","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"Unknown column 'id' in 'field list'","name":"Error","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)"},"level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","timestamp":"2025-08-15 12:30:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","timestamp":"2025-08-15 12:30:00","version":"1.0.0"}
{"environment":"development","level":"error","message":"Failed to clean expired cache: cacheManager.cleanExpiredCache is not a function","service":"ai-trainer-recommender","stack":"TypeError: cacheManager.cleanExpiredCache is not a function\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:159:32)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-08-15 13:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:00:00","version":"1.0.0"}
{"environment":"development","error":"Unknown column 'id' in 'field list'","level":"error","message":"Database query error:","params":"none","service":"ai-trainer-recommender","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","timestamp":"2025-08-15 13:00:00","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"Unknown column 'id' in 'field list'","name":"Error","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)"},"level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:00:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Starting trending scores update","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:30:00","version":"1.0.0"}
{"environment":"development","error":"Unknown column 'id' in 'field list'","level":"error","message":"Database query error:","params":"none","service":"ai-trainer-recommender","sql":"\n                SELECT id FROM experts WHERE is_active = true\n            ...","timestamp":"2025-08-15 13:30:00","version":"1.0.0"}
{"context":{},"environment":"development","error":{"message":"Unknown column 'id' in 'field list'","name":"Error","stack":"Error: Unknown column 'id' in 'field list'\n    at PromisePool.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at Database.query (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\config\\database.js:89:47)\n    at TrendingService.updateTrendingScores (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\src\\services\\TrendingService.js:21:44)\n    at Task._execution (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\server.js:169:35)\n    at Task.execute (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Timeout.matchTime [as _onTimeout] (D:\\Project\\Web\\pakarai\\ai-trainer\\recommender\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:594:17)"},"level":"error","message":"Failed to update trending scores","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:30:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Trending scores updated","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:30:00","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:41:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Database connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:41:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client connected","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:41:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis client ready","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:41:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection test successful","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:41:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Redis connection established","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:41:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"All services initialized successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:41:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Cron jobs scheduled successfully","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:41:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"🚀 AI Trainer Hub Recommendation Engine running on port 3002","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:41:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"📊 Environment: development","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:41:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"🔗 Health check: http://localhost:3002/health","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:41:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"📚 API docs: http://localhost:3002/api","service":"ai-trainer-recommender","timestamp":"2025-08-15 13:41:49","version":"1.0.0"}
{"code":"ECONNREFUSED","environment":"development","fatal":true,"level":"error","message":"Database connection test failed:","service":"ai-trainer-recommender","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1712:7)","timestamp":"2025-08-16 16:09:15","version":"1.0.0"}
